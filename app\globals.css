@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* #ffffff - white background */
  --foreground: oklch(0.145 0 0); /* #374151 - dark gray text */
  --card: oklch(1 0 0); /* #f0fdf4 - light green card background */
  --card-foreground: oklch(0.145 0 0); /* #15803d - green text on cards */
  --popover: oklch(1 0 0); /* #ffffff - white popover background */
  --popover-foreground: oklch(0.145 0 0); /* #374151 - dark gray popover text */
  --primary: oklch(0.205 0 0); /* #15803d - green primary color */
  --primary-foreground: oklch(0.985 0 0); /* #ffffff - white text on primary */
  --secondary: oklch(0.97 0 0); /* #84cc16 - bright green secondary */
  --secondary-foreground: oklch(0.205 0 0); /* #374151 - dark text on secondary */
  --muted: oklch(0.97 0 0); /* #f0fdf4 - light green muted background */
  --muted-foreground: oklch(0.556 0 0); /* #374151 - dark muted text */
  --accent: oklch(0.97 0 0); /* #84cc16 - bright green accent */
  --accent-foreground: oklch(0.205 0 0); /* #374151 - dark accent text */
  --destructive: oklch(0.577 0.245 27.325); /* #ea580c - orange destructive */
  --destructive-foreground: oklch(0.577 0.245 27.325); /* #ffffff - white destructive text */
  --border: oklch(0.922 0 0); /* #d1d5db - light gray border */
  --input: oklch(0.922 0 0); /* #ffffff - white input background */
  --ring: oklch(0.708 0 0); /* #84cc16 - bright green focus ring */
  --chart-1: oklch(0.646 0.222 41.116); /* #15803d - green chart color */
  --chart-2: oklch(0.6 0.118 184.704); /* #84cc16 - bright green chart color */
  --chart-3: oklch(0.398 0.07 227.392); /* #f97316 - orange chart color */
  --chart-4: oklch(0.828 0.189 84.429); /* #ea580c - red-orange chart color */
  --chart-5: oklch(0.769 0.188 70.08); /* #059669 - teal chart color */
  --radius: 0.625rem; /* 8px corner radius */
  --sidebar: oklch(0.985 0 0); /* #f0fdf4 - light green sidebar */
  --sidebar-foreground: oklch(0.145 0 0); /* #374151 - dark sidebar text */
  --sidebar-primary: oklch(0.205 0 0); /* #15803d - green sidebar primary */
  --sidebar-primary-foreground: oklch(0.985 0 0); /* #ffffff - white sidebar primary text */
  --sidebar-accent: oklch(0.97 0 0); /* #84cc16 - bright green sidebar accent */
  --sidebar-accent-foreground: oklch(0.205 0 0); /* #374151 - dark sidebar accent text */
  --sidebar-border: oklch(0.922 0 0); /* #d1d5db - light gray sidebar border */
  --sidebar-ring: oklch(0.708 0 0); /* #84cc16 - bright green sidebar focus ring */
  --font-manrope: "Manrope", sans-serif;
  --font-geist: "Geist", serif;
}

.dark {
  --background: oklch(0.145 0 0); /* Very dark background */
  --foreground: oklch(0.985 0 0); /* Light text */
  --card: oklch(0.145 0 0); /* Dark green card background */
  --card-foreground: oklch(0.985 0 0); /* Light green card text */
  --popover: oklch(0.145 0 0); /* Dark popover background */
  --popover-foreground: oklch(0.985 0 0); /* Light popover text */
  --primary: oklch(0.985 0 0); /* Bright green primary in dark mode */
  --primary-foreground: oklch(0.205 0 0); /* Dark text on bright primary */
  --secondary: oklch(0.269 0 0); /* Darker green secondary */
  --secondary-foreground: oklch(0.985 0 0); /* Light text on secondary */
  --muted: oklch(0.269 0 0); /* Dark green muted background */
  --muted-foreground: oklch(0.708 0 0); /* Medium gray muted text */
  --accent: oklch(0.269 0 0); /* Bright green accent */
  --accent-foreground: oklch(0.985 0 0); /* Dark accent text */
  --destructive: oklch(0.396 0.141 25.723); /* Darker orange destructive */
  --destructive-foreground: oklch(0.637 0.237 25.331); /* Light destructive text */
  --border: oklch(0.269 0 0); /* Dark border */
  --input: oklch(0.269 0 0); /* Dark green input background */
  --ring: oklch(0.439 0 0); /* Bright green focus ring */
  --chart-1: oklch(0.488 0.243 264.376); /* Bright green chart color */
  --chart-2: oklch(0.696 0.17 162.48); /* Dark green chart color */
  --chart-3: oklch(0.769 0.188 70.08); /* Orange chart color */
  --chart-4: oklch(0.627 0.265 303.9); /* Red-orange chart color */
  --chart-5: oklch(0.645 0.246 16.439); /* Teal chart color */
  --sidebar: oklch(0.205 0 0); /* Dark green sidebar */
  --sidebar-foreground: oklch(0.985 0 0); /* Light sidebar text */
  --sidebar-primary: oklch(0.488 0.243 264.376); /* Bright green sidebar primary */
  --sidebar-primary-foreground: oklch(0.985 0 0); /* Dark sidebar primary text */
  --sidebar-accent: oklch(0.269 0 0); /* Dark green sidebar accent */
  --sidebar-accent-foreground: oklch(0.985 0 0); /* Light sidebar accent text */
  --sidebar-border: oklch(0.269 0 0); /* Dark sidebar border */
  --sidebar-ring: oklch(0.439 0 0); /* Bright green sidebar focus ring */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: var(--font-manrope);
  --font-serif: var(--font-geist);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
