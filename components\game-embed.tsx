'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Maximize2, RotateCcw } from 'lucide-react';
import ChiikawaGameComponent from './chiikawa-game';

interface Game {
  id: string;
  name: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
}

const games: Game[] = [
  {
    id: 'chiikawa-puzzle',
    name: 'Chiikawa Puzzle',
    description: 'Test your memory and recreate the adorable <PERSON>ika<PERSON> character!',
    difficulty: 'Medium',
    estimatedTime: '3-5 min'
  }
];

export default function GameEmbed() {
  const [selectedGame] = useState<Game>(games[0]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [key, setKey] = useState(0);

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleRestart = () => {
    setKey(prev => prev + 1);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-pink-100 to-blue-100 p-4">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h3 className="text-xl font-bold text-gray-800">{selectedGame.name}</h3>
                <p className="text-sm text-gray-600">{selectedGame.description}</p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRestart}
                  className="flex items-center gap-1"
                >
                  <RotateCcw className="w-4 h-4" />
                  Restart
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFullscreen}
                  className="flex items-center gap-1"
                >
                  <Maximize2 className="w-4 h-4" />
                  {isFullscreen ? 'Exit' : 'Fullscreen'}
                </Button>
              </div>
            </div>
            <div className="flex gap-4 text-sm text-gray-600">
              <span className="bg-white px-2 py-1 rounded-full">
                Difficulty: {selectedGame.difficulty}
              </span>
              <span className="bg-white px-2 py-1 rounded-full">
                Time: {selectedGame.estimatedTime}
              </span>
            </div>
          </div>
          
          <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
            <div key={key} className={`w-full ${isFullscreen ? 'h-screen flex items-center justify-center' : 'h-[600px]'}`}>
              <ChiikawaGameComponent />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
