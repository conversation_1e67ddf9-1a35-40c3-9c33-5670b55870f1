/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  // 启用压缩
  compress: true,
  // 优化构建
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-slot'],
  },
  // Webpack优化
  webpack: (config, { dev, isServer }) => {
    // 生产环境优化
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          pixi: {
            test: /[\\/]node_modules[\\/]pixi\.js[\\/]/,
            name: 'pixi',
            chunks: 'all',
            priority: 10,
          },
        },
      };
    }
    return config;
  },
  // 性能优化
  poweredByHeader: false,
  generateEtags: false,
}

export default nextConfig
