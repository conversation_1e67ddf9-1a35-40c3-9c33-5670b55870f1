import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON>, Heart, Zap, Trophy, Users, Smartphone } from "lucide-react"

const features = [
  {
    icon: Brain,
    title: "Brain Training Puzzles",
    description:
      "Challenge your mind with carefully designed puzzle mechanics that improve cognitive function and problem-solving skills.",
  },
  {
    icon: Heart,
    title: "Adorable Chiikawa Characters",
    description:
      "Enjoy puzzles featuring beloved Chiikawa characters in their cute and charming art style that brings joy to every level.",
  },
  {
    icon: Zap,
    title: "Instant Play - No Download",
    description:
      "Jump straight into the action with our browser-based games. No installation required, just pure puzzle fun.",
  },
  {
    icon: Trophy,
    title: "Progressive Difficulty",
    description:
      "Start easy and work your way up through increasingly challenging levels designed to keep you engaged and motivated.",
  },
  {
    icon: Users,
    title: "Family Friendly",
    description:
      "Perfect for players of all ages with content that's safe, educational, and entertaining for the whole family.",
  },
  {
    icon: Smartphone,
    title: "Mobile Optimized",
    description:
      "Play seamlessly on any device - desktop, tablet, or mobile. Our responsive design ensures great gameplay everywhere.",
  },
]

export function GameFeatures() {
  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="font-serif text-3xl sm:text-4xl font-bold text-foreground mb-4">
            Why Choose Chiikawa Puzzle Games?
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Discover what makes our free online Chiikawa puzzle games the perfect choice for puzzle enthusiasts, casual
            gamers, and fans of Japanese kawaii culture.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
                  <feature.icon className="w-8 h-8 text-primary" />
                </div>
                <CardTitle className="font-serif text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center leading-relaxed">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
