'use client';

import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

export function PerformanceMonitor() {
  useEffect(() => {
    // 只在生产环境监控性能
    if (process.env.NODE_ENV !== 'production') return;

    const metrics: PerformanceMetrics = {};

    // 监控 First Contentful Paint
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          metrics.fcp = entry.startTime;
          console.log('FCP:', entry.startTime);
        }
      }
    });

    // 监控 Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      metrics.lcp = lastEntry.startTime;
      console.log('LCP:', lastEntry.startTime);
    });

    // 监控 Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0;
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      metrics.cls = clsValue;
      console.log('CLS:', clsValue);
    });

    // 监控 First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        metrics.fid = (entry as any).processingStart - entry.startTime;
        console.log('FID:', metrics.fid);
      }
    });

    try {
      observer.observe({ entryTypes: ['paint'] });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (error) {
      console.warn('Performance monitoring not supported:', error);
    }

    // 监控资源加载时间
    const checkResourceTiming = () => {
      const resources = performance.getEntriesByType('resource');
      const largeResources = resources.filter(
        (resource) => resource.transferSize > 100000 // 大于100KB的资源
      );
      
      if (largeResources.length > 0) {
        console.warn('Large resources detected:', largeResources);
      }
    };

    // 页面加载完成后检查资源
    window.addEventListener('load', checkResourceTiming);

    // 监控内存使用（如果支持）
    const checkMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        console.log('Memory usage:', {
          used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
          total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
        });
      }
    };

    // 定期检查内存使用
    const memoryInterval = setInterval(checkMemoryUsage, 30000);

    return () => {
      observer.disconnect();
      lcpObserver.disconnect();
      clsObserver.disconnect();
      fidObserver.disconnect();
      window.removeEventListener('load', checkResourceTiming);
      clearInterval(memoryInterval);
    };
  }, []);

  return null; // 这是一个监控组件，不渲染任何内容
}

// Web Vitals 报告函数
export function reportWebVitals(metric: any) {
  if (process.env.NODE_ENV === 'production') {
    console.log(metric);
    
    // 这里可以发送到分析服务
    // 例如: analytics.track('Web Vital', metric);
  }
}
