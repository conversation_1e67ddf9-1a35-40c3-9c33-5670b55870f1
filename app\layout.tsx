import type { Metadata } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { PerformanceMonitor } from '@/components/performance-monitor'
import './globals.css'

export const metadata: Metadata = {
  title: 'Chiikawa Puzzle Game | Free Online Kawaii Brain Teasers & Challenges',
  description: 'Play free Chiikawa puzzle games featuring adorable Japanese characters. Enjoy brain training puzzles, progressive difficulty levels, and family-friendly entertainment. No download required!',
  generator: 'Chiikawa Puzzle',
  keywords: 'Chiikawa puzzle game, kawaii puzzles, brain training games, Japanese character puzzles, free online puzzles, Chiikawa characters, family-friendly games',
  icons: {
    icon: '/chiikawa-puzzle-icon.png',
    shortcut: '/chiikawa-puzzle-icon.png',
    apple: '/chiikawa-puzzle-icon.png',
  },
  openGraph: {
    title: 'Chiikawa Puzzle Game | Free Online Kawaii Brain Teasers',
    description: 'Play free Chiikawa puzzle games featuring adorable Japanese characters. Brain training puzzles for all ages!',
    images: ['/chiikawa-puzzle-icon.png'],
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Chiikawa Puzzle Game | Free Online Kawaii Brain Teasers',
    description: 'Play free Chiikawa puzzle games featuring adorable Japanese characters. Brain training puzzles for all ages!',
    images: ['/chiikawa-puzzle-icon.png'],
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/chiikawa-puzzle-icon.png" type="image/png" />
        <link rel="shortcut icon" href="/chiikawa-puzzle-icon.png" type="image/png" />
        <link rel="apple-touch-icon" href="/chiikawa-puzzle-icon.png" />
        <meta name="theme-color" content="#ff69b4" />
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body>
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  )
}
