'use client';

import { useState, lazy, Suspense } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Maximize2, RotateCcw, Play } from 'lucide-react';

// 懒加载游戏组件
const ChiikawaGameComponent = lazy(() => import('./chiikawa-game'));

interface Game {
  id: string;
  name: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
}

const games: Game[] = [
  {
    id: 'chiikawa-puzzle',
    name: 'Chiikawa Memory Puzzle',
    description: 'Test your memory and recreate the adorable <PERSON><PERSON><PERSON> character!',
    difficulty: 'Medium',
    estimatedTime: '3-5 min'
  }
];

// 游戏加载占位符组件
function GameLoadingPlaceholder() {
  return (
    <div className="w-full h-[600px] flex flex-col items-center justify-center bg-gradient-to-br from-pink-50 to-blue-50 rounded-lg">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mb-4"></div>
      <p className="text-gray-600 text-lg font-medium">Loading Game...</p>
      <p className="text-gray-400 text-sm mt-2">Preparing your puzzle experience</p>
    </div>
  );
}

// 游戏启动按钮组件
function GameStartButton({ onStart }: { onStart: () => void }) {
  return (
    <div className="w-full h-[600px] flex flex-col items-center justify-center bg-gradient-to-br from-pink-50 to-blue-50 rounded-lg">
      <div className="text-center space-y-6">
        <div className="w-24 h-24 mx-auto bg-pink-100 rounded-full flex items-center justify-center">
          <Play className="w-12 h-12 text-pink-600" />
        </div>
        <div>
          <h3 className="text-2xl font-bold text-gray-800 mb-2">Ready to Play?</h3>
          <p className="text-gray-600 mb-6">Click to start the Chiikawa Memory Puzzle</p>
        </div>
        <Button 
          onClick={onStart}
          size="lg"
          className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-3 text-lg"
        >
          <Play className="w-5 h-5 mr-2" />
          Start Game
        </Button>
      </div>
    </div>
  );
}

export default function LazyGameEmbed() {
  const [selectedGame] = useState<Game>(games[0]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [key, setKey] = useState(0);
  const [gameStarted, setGameStarted] = useState(false);

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleRestart = () => {
    setKey(prev => prev + 1);
  };

  const handleStartGame = () => {
    setGameStarted(true);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-pink-100 to-blue-100 p-4">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h3 className="text-xl font-bold text-gray-800">{selectedGame.name}</h3>
                <p className="text-sm text-gray-600">{selectedGame.description}</p>
              </div>
              {gameStarted && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRestart}
                    className="flex items-center gap-1"
                  >
                    <RotateCcw className="w-4 h-4" />
                    Restart
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleFullscreen}
                    className="flex items-center gap-1"
                  >
                    <Maximize2 className="w-4 h-4" />
                    {isFullscreen ? 'Exit' : 'Fullscreen'}
                  </Button>
                </div>
              )}
            </div>
            <div className="flex gap-4 text-sm text-gray-600">
              <span className="bg-white px-2 py-1 rounded-full">
                Difficulty: {selectedGame.difficulty}
              </span>
              <span className="bg-white px-2 py-1 rounded-full">
                Time: {selectedGame.estimatedTime}
              </span>
            </div>
          </div>
          
          <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
            <div className={`w-full ${isFullscreen ? 'h-screen flex items-center justify-center' : 'h-[600px]'}`}>
              {!gameStarted ? (
                <GameStartButton onStart={handleStartGame} />
              ) : (
                <Suspense fallback={<GameLoadingPlaceholder />}>
                  <div key={key}>
                    <ChiikawaGameComponent />
                  </div>
                </Suspense>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
