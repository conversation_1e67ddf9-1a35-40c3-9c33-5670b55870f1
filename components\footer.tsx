import Link from "next/link"
import { Heart, Mail, MessageCircle } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-card border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">C</span>
              </div>
              <span className="font-serif font-bold text-xl text-card-foreground">Chiikawa Puzzle</span>
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Free online Chiikawa puzzle games featuring your favorite kawaii characters. Play brain-training puzzles
              anytime, anywhere!
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-serif font-semibold text-card-foreground">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Home
              </Link>
              <Link
                href="/privacy"
                className="block text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                Privacy Policy
              </Link>
              <Link href="/faq" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                FAQ
              </Link>
            </div>
          </div>

          {/* Game Info */}
          <div className="space-y-4">
            <h3 className="font-serif font-semibold text-card-foreground">Game Features</h3>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">✓ 100% Free Online</p>
              <p className="text-sm text-muted-foreground">✓ No Download Required</p>
              <p className="text-sm text-muted-foreground">✓ Mobile Friendly</p>
              <p className="text-sm text-muted-foreground">✓ All Ages Welcome</p>
            </div>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="font-serif font-semibold text-card-foreground">Contact</h3>
            <div className="space-y-2">
              <Link
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                <Mail className="h-4 w-4" />
                <EMAIL>
              </Link>
              <Link
                href="#"
                className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                <MessageCircle className="h-4 w-4" />
                Support Chat
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-border mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-sm text-muted-foreground">© 2024 Chiikawa Puzzle. All rights reserved.</p>
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            Made with <Heart className="h-4 w-4 text-red-500" /> for puzzle lovers
          </div>
        </div>
      </div>
    </footer>
  )
}
