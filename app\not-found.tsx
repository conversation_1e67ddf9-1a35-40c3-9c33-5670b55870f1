"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Home, ArrowLeft } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center space-y-8">
        {/* 404 Illustration */}
        <div className="relative">
          <div className="text-9xl font-bold text-primary/20 select-none">404</div>
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src="/placeholder.svg?height=200&width=200"
              alt="Lost Chiikawa character"
              className="w-32 h-32 object-contain"
            />
          </div>
        </div>

        {/* Error Message */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="font-serif text-3xl text-card-foreground">Oops! Page Not Found</CardTitle>
            <CardDescription className="text-lg">
              It looks like this puzzle piece is missing! The page you're looking for doesn't exist.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-muted-foreground leading-relaxed">
              Don't worry - even Chiikawa gets lost sometimes! The page you're trying to reach might have been moved,
              deleted, or you may have typed the URL incorrectly.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg">
                <Link href="/">
                  <Home className="mr-2 h-5 w-5" />
                  Back to Home
                </Link>
              </Button>
              <Button variant="outline" size="lg" onClick={() => window.history.back()}>
                <ArrowLeft className="mr-2 h-5 w-5" />
                Go Back
              </Button>
            </div>

            {/* Helpful Links */}
            <div className="pt-6 border-t border-border">
              <h3 className="font-serif font-semibold text-card-foreground mb-4">Popular Pages</h3>
              <div className="grid sm:grid-cols-3 gap-3">
                <Link
                  href="/"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-muted"
                >
                  🏠 Home & Games
                </Link>
                <Link
                  href="/faq"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-muted"
                >
                  ❓ FAQ
                </Link>
                <Link
                  href="/privacy"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-muted"
                >
                  🔒 Privacy Policy
                </Link>
              </div>
            </div>

            {/* Fun Message */}
            <div className="bg-primary/5 rounded-lg p-4 mt-6">
              <p className="text-sm text-muted-foreground">
                💡 <strong>Pro tip:</strong> While you're here, why not try our free Chiikawa puzzle games? They're much
                easier to find than this missing page!
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Decorative Elements */}
        <div className="flex justify-center gap-4 opacity-50">
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-accent rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
        </div>
      </div>
    </div>
  )
}
