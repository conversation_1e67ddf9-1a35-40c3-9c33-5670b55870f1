{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "build:analyze": "ANALYZE=true next build", "dev": "next dev", "lint": "next lint", "start": "next start", "pages:build": "next build", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy", "optimize:images": "node scripts/optimize-images.js", "performance:check": "npm run build && npm run optimize:images"}, "dependencies": {"@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-slot": "1.1.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "geist": "^1.3.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.6", "pixi.js": "^8.12.0", "react": "^19", "react-dom": "^19", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.5", "@tailwindcss/postcss": "^4.1.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tw-animate-css": "1.3.3", "typescript": "^5", "wrangler": "^3.78.12"}}