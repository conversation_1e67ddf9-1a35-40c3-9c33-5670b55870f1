import { But<PERSON> } from "@/components/ui/button"
import { Play, Star, Users } from "lucide-react"

export function HeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-card to-background py-20 lg:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Hero Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight">
                Play Free <span className="text-primary">Chiikawa Puzzle</span> Games Online
              </h1>
              <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
                Dive into the adorable world of Chiikawa with our collection of free online puzzle games. Challenge your
                mind with cute Japanese character puzzles, brain teasers, and engaging levels perfect for puzzle
                enthusiasts of all ages.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="text-lg px-8 py-6">
                <Play className="mr-2 h-5 w-5" />
                Play Now - Free!
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6 bg-transparent">
                Learn More
              </Button>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap gap-8 pt-8">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-accent" />
                <span className="text-sm text-muted-foreground">4.8/5 Rating</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-accent" />
                <span className="text-sm text-muted-foreground">50K+ Players</span>
              </div>
              <div className="flex items-center gap-2">
                <Play className="h-5 w-5 text-accent" />
                <span className="text-sm text-muted-foreground">100% Free</span>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative">
            <div className="aspect-square bg-gradient-to-br from-primary/20 to-accent/20 rounded-3xl p-8 flex items-center justify-center">
              <img
                src="/chiikawa-puzzle.png"
                alt="Chiikawa puzzle game characters"
                className="w-full h-full object-contain rounded-2xl"
              />
            </div>
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-accent rounded-full flex items-center justify-center animate-bounce">
              <span className="text-2xl">🧩</span>
            </div>
            <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-primary rounded-full flex items-center justify-center animate-pulse">
              <span className="text-xl">⭐</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
