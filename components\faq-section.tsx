import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion"

const faqs = [
  {
    question: "Are Chiikawa puzzle games really free to play online?",
    answer:
      "Yes! All our Chiikawa puzzle games are completely free to play online. You don't need to download anything or create an account. Simply visit our website and start playing immediately in your browser.",
  },
  {
    question: "What devices can I play Chiikawa puzzles on?",
    answer:
      "Our games are designed to work on all devices including desktop computers, laptops, tablets, and smartphones. The responsive design ensures optimal gameplay experience regardless of your screen size.",
  },
  {
    question: "Do I need to know about Chiikawa characters to enjoy the puzzles?",
    answer:
      "Not at all! While fans of the Chiikawa series will appreciate the character references, the puzzles are designed to be enjoyable for everyone. The cute art style and engaging gameplay make them perfect for any puzzle enthusiast.",
  },
  {
    question: "How many puzzle levels are available?",
    answer:
      "We offer a wide variety of puzzle levels ranging from simple 4-piece puzzles for beginners to complex 100+ piece challenges for advanced players. New puzzles are added regularly to keep the experience fresh and exciting.",
  },
  {
    question: "Can I save my progress in the puzzle games?",
    answer:
      "Yes! Your progress is automatically saved in your browser's local storage. You can pause a puzzle and return to it later without losing your progress. However, clearing your browser data will reset your saved games.",
  },
  {
    question: "Are there different types of puzzle games available?",
    answer:
      "We offer various puzzle types including traditional jigsaw puzzles, sliding block puzzles, pattern matching games, and logic puzzles. Each type features the beloved Chiikawa characters in different scenarios.",
  },
  {
    question: "Is there a time limit for solving puzzles?",
    answer:
      "Most puzzles can be solved at your own pace without time pressure. However, we also offer timed challenge modes for players who want to test their speed-solving skills and compete on leaderboards.",
  },
  {
    question: "Can children play these puzzle games safely?",
    answer:
      "Yes! Our Chiikawa puzzle games are family-friendly and suitable for all ages. The content is safe, educational, and promotes cognitive development. Parents can feel confident letting their children play these games.",
  },
  {
    question: "Do you offer hints if I get stuck on a puzzle?",
    answer:
      "Yes! We have an intelligent hint system that provides helpful clues without spoiling the solution. You can get hints about piece placement, color matching, or general solving strategies whenever you need assistance.",
  },
  {
    question: "How often do you add new Chiikawa puzzle content?",
    answer:
      "We regularly update our game collection with new puzzles, characters, and features. Follow our social media or check back frequently to discover the latest Chiikawa puzzle adventures and seasonal content.",
  },
]

export function FaqSection() {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground">
              Find answers to common questions about our free online Chiikawa puzzle games, gameplay mechanics, and
              features.
            </p>
          </div>

          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="bg-card rounded-lg px-6">
                <AccordionTrigger className="text-left font-semibold text-card-foreground hover:text-primary">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground leading-relaxed pt-2">{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  )
}
