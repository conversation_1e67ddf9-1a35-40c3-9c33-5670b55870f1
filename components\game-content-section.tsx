import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function GameContentSection() {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto space-y-12">
          {/* Introduction */}
          <div className="text-center space-y-4">
            <h2 className="font-serif text-3xl sm:text-4xl font-bold text-foreground">
              The Ultimate Chiikawa Puzzle Experience
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Immerse yourself in the delightful world of Chiikawa through our comprehensive collection of free online
              puzzle games, brain teasers, and interactive challenges.
            </p>
          </div>

          {/* Main Content Cards */}
          <div className="grid lg:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secondary">Featured</Badge>
                  <Badge variant="outline">Free Online</Badge>
                </div>
                <CardTitle className="font-serif text-2xl">What Makes Chiikawa Puzzles Special?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <img
                  src="/kawaii-puzzle-chiikawa.png"
                  alt="Chiikawa characters solving puzzles"
                  width={400}
                  height={192}
                  loading="lazy"
                  className="w-full h-48 object-cover rounded-lg"
                />
                <p className="text-muted-foreground leading-relaxed">
                  Chiikawa puzzle games combine the beloved Japanese kawaii aesthetic with engaging puzzle mechanics.
                  These free online games feature the adorable characters from the popular Chiikawa series, including
                  the main character Chiikawa, Hachiware, and Usagi, in various puzzle-solving adventures.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  Each puzzle is carefully crafted to provide both entertainment and cognitive benefits, making them
                  perfect for players who want to exercise their minds while enjoying cute, colorful graphics and
                  charming character interactions.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secondary">Brain Training</Badge>
                  <Badge variant="outline">All Ages</Badge>
                </div>
                <CardTitle className="font-serif text-2xl">Puzzle Types & Game Modes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <img
                  src="/puzzle-game-types.png"
                  alt="Various puzzle game types"
                  width={400}
                  height={192}
                  loading="lazy"
                  className="w-full h-48 object-cover rounded-lg"
                />
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold text-card-foreground">Jigsaw Puzzles</h4>
                    <p className="text-sm text-muted-foreground">
                      Classic jigsaw puzzles featuring beautiful Chiikawa artwork
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-card-foreground">Sliding Block Puzzles</h4>
                    <p className="text-sm text-muted-foreground">
                      Challenge your spatial reasoning with sliding tile games
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-card-foreground">Pattern Matching</h4>
                    <p className="text-sm text-muted-foreground">
                      Match Chiikawa characters and objects in various configurations
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-card-foreground">Logic Puzzles</h4>
                    <p className="text-sm text-muted-foreground">
                      Solve complex logic challenges with your favorite characters
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
