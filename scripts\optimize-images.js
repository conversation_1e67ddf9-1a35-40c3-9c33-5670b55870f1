const fs = require('fs');
const path = require('path');

// 图片优化建议脚本
// 由于我们无法直接安装图片处理库，这个脚本提供优化建议

const publicDir = path.join(process.cwd(), 'public');

function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return Math.round(stats.size / 1024);
}

function analyzeImages() {
  const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
  const largeImages = [];
  
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDirectory(filePath);
      } else {
        const ext = path.extname(file).toLowerCase();
        if (imageExtensions.includes(ext)) {
          const sizeKB = getFileSizeInKB(filePath);
          const relativePath = path.relative(publicDir, filePath);
          
          if (sizeKB > 100) { // 大于100KB的图片
            largeImages.push({
              path: relativePath,
              size: sizeKB,
              ext: ext
            });
          }
        }
      }
    });
  }
  
  scanDirectory(publicDir);
  return largeImages;
}

function generateOptimizationReport() {
  const largeImages = analyzeImages();
  
  console.log('🖼️  图片优化报告');
  console.log('================');
  console.log();
  
  if (largeImages.length === 0) {
    console.log('✅ 所有图片都已优化！');
    return;
  }
  
  console.log(`发现 ${largeImages.length} 个需要优化的大图片：`);
  console.log();
  
  largeImages.sort((a, b) => b.size - a.size);
  
  largeImages.forEach((img, index) => {
    console.log(`${index + 1}. ${img.path}`);
    console.log(`   大小: ${img.size}KB`);
    console.log(`   建议: ${getOptimizationSuggestion(img)}`);
    console.log();
  });
  
  console.log('🛠️  优化建议：');
  console.log('1. 使用在线工具压缩图片：');
  console.log('   - TinyPNG (https://tinypng.com/)');
  console.log('   - Squoosh (https://squoosh.app/)');
  console.log('   - ImageOptim (Mac)');
  console.log();
  console.log('2. 转换为现代格式：');
  console.log('   - 将PNG转换为WebP格式');
  console.log('   - 为不同屏幕尺寸创建多个版本');
  console.log();
  console.log('3. 实现响应式图片：');
  console.log('   - 使用 <picture> 元素');
  console.log('   - 提供多种格式和尺寸');
  console.log();
}

function getOptimizationSuggestion(img) {
  if (img.size > 1000) {
    return `压缩至少70% (目标: <300KB)，考虑转换为WebP格式`;
  } else if (img.size > 500) {
    return `压缩50-60% (目标: <200KB)，转换为WebP格式`;
  } else if (img.size > 200) {
    return `压缩30-40% (目标: <100KB)`;
  } else {
    return `轻度压缩10-20%`;
  }
}

// 生成优化后的文件名建议
function generateOptimizedFilenames() {
  const largeImages = analyzeImages();
  
  console.log('📝 优化后的文件命名建议：');
  console.log('========================');
  console.log();
  
  largeImages.forEach(img => {
    const baseName = path.basename(img.path, path.extname(img.path));
    const dir = path.dirname(img.path);
    
    console.log(`原文件: ${img.path}`);
    console.log(`建议的优化版本:`);
    console.log(`  - ${dir}/${baseName}-640w.webp (移动端)`);
    console.log(`  - ${dir}/${baseName}-1200w.webp (桌面端)`);
    console.log(`  - ${dir}/${baseName}-optimized.png (备用格式)`);
    console.log();
  });
}

// 运行分析
if (require.main === module) {
  generateOptimizationReport();
  console.log();
  generateOptimizedFilenames();
}
