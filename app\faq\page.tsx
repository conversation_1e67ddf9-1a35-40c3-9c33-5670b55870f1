import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { FaqSection } from "@/components/faq-section"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle, Mail } from "lucide-react"

export const metadata = {
  title: "FAQ - Chiikawa Puzzle Games | Frequently Asked Questions",
  description:
    "Find answers to common questions about Chiikawa puzzle games. Learn about gameplay, features, and how to get the most out of your free online puzzle experience.",
}

export default function FaqPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="font-serif text-4xl font-bold text-foreground mb-4">Frequently Asked Questions</h1>
              <p className="text-lg text-muted-foreground">
                Everything you need to know about playing Chiikawa puzzle games online for free
              </p>
            </div>

            {/* Quick Help Section */}
            <div className="grid md:grid-cols-2 gap-6 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="font-serif text-xl flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-primary" />
                    Need Quick Help?
                  </CardTitle>
                  <CardDescription>Get instant answers to common questions</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Most questions about our free Chiikawa puzzle games are answered below. If you can't find what
                    you're looking for, don't hesitate to reach out!
                  </p>
                  <Button variant="outline" className="w-full bg-transparent">
                    <MessageCircle className="mr-2 h-4 w-4" />
                    Live Chat Support
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="font-serif text-xl flex items-center gap-2">
                    <Mail className="h-5 w-5 text-primary" />
                    Contact Support
                  </CardTitle>
                  <CardDescription>Get personalized help via email</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    For detailed questions or technical issues with our puzzle games, our support team is here to help
                    you get back to playing.
                  </p>
                  <Button variant="outline" className="w-full bg-transparent">
                    <Mail className="mr-2 h-4 w-4" />
                    Email Support
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Main FAQ Section */}
            <FaqSection />

            {/* Additional Help Section */}
            <div className="mt-16 bg-card rounded-2xl p-8">
              <div className="text-center space-y-4">
                <h2 className="font-serif text-2xl font-bold text-card-foreground">Still Have Questions?</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Our support team is always ready to help you enjoy the best possible experience with our free online
                  Chiikawa puzzle games. Don't hesitate to reach out!
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                  <Button size="lg">
                    <MessageCircle className="mr-2 h-5 w-5" />
                    Start Live Chat
                  </Button>
                  <Button variant="outline" size="lg">
                    <Mail className="mr-2 h-5 w-5" />
                    Send Email
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
